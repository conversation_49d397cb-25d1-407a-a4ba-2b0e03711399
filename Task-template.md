[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:Check if we have a system with time based already in our code for other providers, aka not in our new fallback system DESCRIPTION:
-[ ] NAME:how do they handle timeouts? DESCRIPTION:
-[ ] NAME:do they save somewhere what models was used already`? if so how do they reset it for next user interaction? DESCRIPTION:
-[ ] NAME:dig deep into the code DESCRIPTION:
-[ ] NAME:then think about how we can use existing systems for our fallback DESCRIPTION:
-[ ] NAME:also think if we have to use the same system for our fallback as for the rate limit system? DESCRIPTION:
-[ ] NAME:adapt that @c:\Users\<USER>\Desktop\Web Projects\CustomCline/RATE_LIMIT_RECOVERY_SYSTEM.md with your findings DESCRIPTION: