# 🔨 Building Cline Extension - Complete Guide

## 📋 Prerequisites

### Required Software
1. **Node.js** (v18.0.0 or higher)
   - Download from: https://nodejs.org/
   - Verify installation: `node --version`

2. **npm** (comes with Node.js)
   - Verify installation: `npm --version`

3. **Visual Studio Code** (for testing)
   - Download from: https://code.visualstudio.com/

4. **Git** (for version control)
   - Download from: https://git-scm.com/

### Optional Tools
- **vsce** (Visual Studio Code Extension Manager)
  ```bash
  npm install -g @vscode/vsce
  ```

## 🚀 Build Process

### Step 1: Install Dependencies

```bash
# Navigate to project root
cd "c:\Users\<USER>\Desktop\Web Projects\CustomCline"

# Install main project dependencies
npm install

# Install webview dependencies
cd webview-ui
npm install
cd ..
```

### Step 2: Build the Extension

```bash
# Clean previous builds (optional)
npm run clean

# Build the extension for production
npm run package
```

**What this does:**
- Runs TypeScript type checking (`npm run check-types`)
- Builds the webview UI (`npm run build:webview`)
- Runs ESLint for code quality (`npm run lint`)
- Bundles the extension using esbuild (`node esbuild.js --production`)

### Step 3: Create .vsix Package

#### Option A: Using npm script (Recommended)
```bash
# This will create the .vsix file automatically
npm run vscode:prepublish
```

#### Option B: Using vsce directly
```bash
# Install vsce if not already installed
npm install -g @vscode/vsce

# Create the .vsix package
vsce package
```

#### Option C: Manual vsce with specific version
```bash
# Package with specific version
vsce package --out cline-custom-3.18.0.vsix

# Package with pre-release flag
vsce package --pre-release
```

## 📦 Output Files

After successful build, you'll find:

1. **Main Extension Bundle**: `./dist/extension.js`
2. **Webview Build**: `./webview-ui/build/`
3. **VSIX Package**: `./claude-dev-3.18.0.vsix` (or similar name)

## 🔧 Installation Methods

### Method 1: VS Code Command Palette
1. Open VS Code
2. Press `Ctrl+Shift+P` (Windows/Linux) or `Cmd+Shift+P` (Mac)
3. Type "Extensions: Install from VSIX..."
4. Select your `.vsix` file
5. Restart VS Code when prompted

### Method 2: VS Code Extensions View
1. Open VS Code
2. Go to Extensions view (`Ctrl+Shift+X`)
3. Click the "..." menu in the top-right
4. Select "Install from VSIX..."
5. Choose your `.vsix` file

### Method 3: Command Line
```bash
# Using code command (if available)
code --install-extension ./claude-dev-3.18.0.vsix

# Force reinstall if already installed
code --install-extension ./claude-dev-3.18.0.vsix --force
```

## 🛠️ Development Build

For development with hot reloading:

```bash
# Start development mode
npm run watch

# In another terminal, start webview development
npm run dev:webview
```

This will:
- Watch TypeScript files for changes
- Rebuild automatically
- Enable hot reloading for webview

## 🧪 Testing the Build

### Unit Tests
```bash
# Run unit tests
npm run test:unit

# Run with coverage
npm run test:coverage
```

### Integration Tests
```bash
# Run integration tests
npm run test:integration

# Run all tests
npm run test
```

### Manual Testing
1. Install the built extension
2. Open a workspace in VS Code
3. Press `Ctrl+Shift+P` and search for "Cline"
4. Test basic functionality:
   - Open Cline chat
   - Configure API provider
   - Test model fallback (if configured)

## 🔍 Troubleshooting

### Common Build Issues

#### 1. Node Modules Issues
```bash
# Clear node modules and reinstall
rm -rf node_modules webview-ui/node_modules
npm run install:all
```

#### 2. TypeScript Compilation Errors
```bash
# Check types without building
npm run check-types

# Fix formatting issues
npm run format:fix
```

#### 3. ESLint Errors
```bash
# Run linting
npm run lint

# Auto-fix linting issues where possible
npx eslint src --ext ts --fix
npx eslint webview-ui/src --ext ts --fix
```

#### 4. Build Cache Issues
```bash
# Clean all build artifacts
npm run clean

# Rebuild everything
npm run package
```

### VSCE Package Issues

#### 1. Missing Files in Package
Check `.vscodeignore` file to ensure required files aren't excluded:
```
# Make sure these are NOT in .vscodeignore
dist/
webview-ui/build/
assets/
model-fallback-config.json
```

#### 2. Package Size Too Large
```bash
# Check package contents
vsce ls

# Package with specific files only
vsce package --baseContentUrl https://github.com/cline/cline/raw/main/
```

#### 3. Version Conflicts
```bash
# Update version in package.json first
npm version patch  # or minor, major

# Then package
vsce package
```

## 📋 Build Verification Checklist

Before distributing your custom build:

- [ ] All dependencies installed successfully
- [ ] TypeScript compilation passes without errors
- [ ] ESLint passes without errors
- [ ] Unit tests pass
- [ ] Extension bundles without errors
- [ ] VSIX package creates successfully
- [ ] Extension installs in VS Code
- [ ] Basic functionality works (chat, API calls)
- [ ] Model fallback system works (if implemented)
- [ ] No console errors in VS Code Developer Tools

## 🎯 Quick Build Commands

```bash
# Full clean build
npm run clean && npm run install:all && npm run package && vsce package

# Development build
npm run compile && npm run watch

# Test build
npm run test && npm run package
```

## 📁 Project Structure

```
CustomCline/
├── dist/                          # Built extension files
├── webview-ui/build/              # Built webview files
├── src/                           # Source code
├── assets/                        # Extension assets
├── model-fallback-config.json     # Your custom config
├── package.json                   # Extension manifest
├── esbuild.js                     # Build configuration
└── claude-dev-3.18.0.vsix        # Final package
```

## 🚀 Distribution

Once built, you can:
1. **Share the .vsix file** directly with others
2. **Publish to VS Code Marketplace** (requires publisher account)
3. **Host on GitHub Releases** for easy distribution
4. **Create installation scripts** for automated deployment

Your custom Cline extension with automated model fallback is now ready for use!
