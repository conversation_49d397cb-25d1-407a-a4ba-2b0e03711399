[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:update your memories if you deem it necessary for our current workflow, there it should aslo be saying that whennever you begin a task and end a task to append the date and time via a tm toolcall and also after setting a task as "done" with the tm MCP that you should create a new task for you internally DESCRIPTION:
-[ ] NAME:double check if 6.2, 6.3, and 6.4 are really implemented in our codebase DESCRIPTION:
-[ ] NAME:Explain your changes in utmost detail DESCRIPTION:
-[ ] NAME:ideas for improvement? check our code deeply for the auto switcher when rate limited write them to a separate .md file DESCRIPTION:
-[ ] NAME:detailed instructions on how to build this project into an .vsix file so we can load the extension DESCRIP<PERSON>ON:
-[ ] NAME:what other ways do we have to use this extension? be detailed and instructive DESCRIPTION:
-[ ] NAME:guide me through the flow of our update to Cline, make diagrams if you can, save it to a markdown file DESCRIPTION:
-[ ] NAME:use ST to determine if we need to use ct7 for our changes from tasks.json, report your findings into codeblock, no changes though DESCRIPTION: